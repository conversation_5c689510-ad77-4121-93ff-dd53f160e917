import type { RetrievalModelConfig } from '@/app/components/workflow/nodes/knowledge-retrieval/types'

export enum RETRIEVE_METHOD {
  semantic = 'semantic_search',
  fullText = 'full_text_search',
  hybrid = 'hybrid_search',
  invertedIndex = 'invertedIndex',
  keywordSearch = 'keyword_search',
}
export enum WeightedScoreEnum {
  SemanticFirst = 'semantic_first',
  KeywordFirst = 'keyword_first',
  Customized = 'customized',
}
export enum RerankingModeEnum {
  RerankingModel = 'reranking_model',
  WeightedScore = 'weighted_score',
}
export enum RETRIEVE_TYPE {
  oneWay = 'single',
  multiWay = 'multiple',
}

export type RetrievalConfig = {
  search_method: RETRIEVE_METHOD
  reranking_enable: boolean
  reranking_model: {
    reranking_provider_name: string
    reranking_model_name: string
  }
  top_k: number
  score_threshold_enabled: boolean
  score_threshold: number
  reranking_mode?: RerankingModeEnum
  weights?: {
    vector_setting: {
      vector_weight: number
      embedding_provider_name?: string
      embedding_model_name?: string
    }
    keyword_setting: {
      keyword_weight: number
    }
  }
}

export type XiyanRetrievalConfig = Required<RetrievalModelConfig>
