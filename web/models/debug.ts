import type { AgentStrategy, ModelModeType, TtsAutoPlay } from '@/types/model'
import type { RETRIEVE_TYPE, RerankingModeEnum, RetrievalConfig } from '@/types/datasets'
import type { ToolItem } from '@/types/tools'

import type { FileUpload } from '@/app/components/base/features/types'
export type Inputs = Record<string, string | number | object>

export enum PromptMode {
  simple = 'simple',
  advanced = 'advanced',
}

export type PromptItem = {
  role?: PromptRole
  text: string
}

export type ChatPromptConfig = {
  prompt: PromptItem[]
}

export type ConversationHistoriesRole = {
  user_prefix: string
  assistant_prefix: string
}
export type CompletionPromptConfig = {
  prompt: PromptItem
  conversation_histories_role: ConversationHistoriesRole
}

export type BlockStatus = {
  context: boolean
  history: boolean
  query: boolean
}

export enum PromptRole {
  system = 'system',
  user = 'user',
  assistant = 'assistant',
}

export type PromptVariable = {
  key: string
  name: string
  type: string // "string" | "number" | "select",
  default?: string | number
  required?: boolean
  options?: string[]
  max_length?: number
  is_context_var?: boolean
  enabled?: boolean
  config?: Record<string, any>
  icon?: string
  icon_background?: string
  'header-input'?: string // 请求信息字段类型
}

export type PromptConfig = {
  prompt_template: string
  prompt_variables: PromptVariable[]
}

export type MoreLikeThisConfig = {
  enabled: boolean
}

export type SuggestedQuestionsAfterAnswerConfig = MoreLikeThisConfig

export type SpeechToTextConfig = MoreLikeThisConfig

// 语音开关 => 方言、音色
export type voiceInputConfig = {
  enabled: boolean
  language?: string
  timbre?: string
  auto_play?: boolean
}
export type voiceConversationConfig = {
  enabled: boolean
  language?: string
  timbre?: string
}
export type TextToSpeechConfig = {
  enabled: boolean
  voice?: string
  language?: string
  autoPlay?: TtsAutoPlay
  voice_input?: voiceInputConfig
  voice_conversation?: voiceConversationConfig
}

export type CitationConfig = MoreLikeThisConfig

export type AnnotationReplyConfig = {
  id: string
  enabled: boolean
  score_threshold: number
  embedding_model: {
    embedding_provider_name: string
    embedding_model_name: string
  }
}

export type ModerationContentConfig = {
  enabled: boolean
  preset_response?: string
}
export type ModerationConfig = MoreLikeThisConfig & {
  type?: string
  config?: {
    keywords?: string
    api_based_extension_id?: string
    inputs_config?: ModerationContentConfig
    outputs_config?: ModerationContentConfig
  } & Partial<Record<string, any>>
}

export type RetrieverResourceConfig = MoreLikeThisConfig
export type AgentConfig = {
  enabled: boolean
  strategy: AgentStrategy
  max_iteration: number
  tools: ToolItem[]
  fork?: boolean
}

// frontend use. Not the same as backend
export type ModelConfig = {
  provider: string // LLM Provider: for example "OPENAI"
  model_id: string
  mode: ModelModeType
  configs: PromptConfig
  opening_statement: string | null
  more_like_this: MoreLikeThisConfig | null
  suggested_questions: string[] | null
  suggested_questions_after_answer: SuggestedQuestionsAfterAnswerConfig | null
  speech_to_text: SpeechToTextConfig | null
  text_to_speech: TextToSpeechConfig | null
  file_upload: FileUpload | null
  retriever_resource: RetrieverResourceConfig | null
  sensitive_word_avoidance: ModerationConfig | null
  annotation_reply: AnnotationReplyConfig | null
  dataSets: any[]
  agentConfig: AgentConfig
  // voiceInput_enabled: string | null
  // voiceConversation_enabled: string | null
  // voiceInput_language: string
  // voiceInput_timbre: string
  // voiceInput_autoPlay?: boolean
  // voiceConversation_language: string
  // voiceConversation_timbre: string
  new_retrieval_model: RetrievalConfig | null
}

export type DatasetConfigs = {
  retrieval_model: RETRIEVE_TYPE
  search_method?: string
  reranking_model?: {
    reranking_model_name: string
    reranking_provider_name: string
  }
  top_k: number
  score_threshold_enabled?: boolean
  score_threshold: number | null | undefined
  datasets?: {
    datasets: {
      enabled: boolean
      id: string
    }[]
  }
  reranking_mode?: RerankingModeEnum
  weights?: {
    vector_setting: {
      vector_weight: number
      embedding_provider_name: string
      embedding_model_name: string
    }
    keyword_setting: {
      keyword_weight: number
    }
  }
  reranking_enable?: boolean
  fork?: boolean
}
export type SpecialDatasetConfigs = {
  retrieval_model: RETRIEVE_TYPE
  reranking_model?: {
    reranking_model_name: string
    reranking_provider_name: string
  }
  top_k: number
  score_threshold_enabled?: boolean
  score_threshold: number | null | undefined
  datasets?: {
    datasets: {
      dataset: {
        enabled: boolean
        id: string
      }
    }[]
  }
  reranking_mode?: RerankingModeEnum
  weights?: {
    vector_setting: {
      vector_weight: number
      embedding_provider_name: string
      embedding_model_name: string
    }
    keyword_setting: {
      keyword_weight: number
    }
  }
  reranking_enable?: boolean
  fork?: boolean
  lack?: boolean
}

export type SavedMessage = {
  id: string
  answer: string
}
