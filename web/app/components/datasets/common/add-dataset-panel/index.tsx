'use client'
import React, { use<PERSON>allback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Divider } from 'antd'
import produce from 'immer'
import { cloneDeep } from 'lodash-es'
import { RiEqualizer2Line } from '@remixicon/react'
import { useShallow } from 'zustand/react/shallow'
import AddDatasetModal from '../add-dataset-modal'
import DatasetItem from '../dataset-item'
import type { RetrievalModelConfig } from './types.ts'
import SettingsModal from './setting-modal'
import ParamsConfig from './params-config'
import { useStore } from '@/app/components/app/store'
import Button from '@/app/components/base/button'
import Modal from '@/app/components/base/modal'
import { useProviderContext } from '@/context/provider-context'
import { useFeaturesStore } from '@/app/components/base/features/hooks'
import { RETRIEVE_METHOD, RerankingModeE<PERSON> } from '@/types/datasets'

import type { DataSet } from '@/models/datasets'
import type { DatasetConfigs } from '@/models/debug'
import type { RetrievalConfig, XiyanRetrievalConfig } from '@/types/datasets'
import type { RetrieverResource } from '@/app/components/base/features/types'

// 公共组件
import { Add } from '@/app/components/base/icons/src/vender/solid/general'
import FeatureCollapse from '@/app/components/base/features/feature-collapse'
import ForkTip from '@/app/components/base/forkTip'
import TextButton from '@/app/components/base/button/text-button'
import { Delete, Edit } from '@/app/components/base/icons/src/vender/line/general'

// 西研知识库配置
import RetrievalMethodConfigXiyan from '@/app/components/datasets/common/retrieval-method-config-xiyan'

type AddDatasetPanelProps = {
  dataSets: DataSet[]
  config: DatasetConfigs
  scene: 'app' | 'workflow'
  onRemove: (id: string) => void
  onSelect: (dataSet: DataSet[]) => void
  onConfig: (dataSet: DatasetConfigs) => void
}
type AddDatasetPanelConetntProps = {
  dataSets: DataSet[]
  forkDatasets: DataSet[]
  config: DatasetConfigs
  scene: 'app' | 'workflow'
  border?: boolean
  onRemove: (id: string) => void
  onConfig: (dataSet: DatasetConfigs) => void
  onAdd: () => void
  onCloseFock: () => void
  onSetting: (dataSet: DataSet) => void
  // onMultipleRetrievalConfigChange: (config: RetrievalModelConfig) => void
  isInApp?: boolean
}
// 面板内容
export const AddDatasetPanelConetnt = React.memo(({
  dataSets,
  forkDatasets,
  config,
  scene,
  border = false,
  onRemove,
  onConfig,
  onAdd,
  onCloseFock,
  onSetting,
  // onMultipleRetrievalConfigChange,
  isInApp = false,
}: AddDatasetPanelConetntProps) => {
  const { t } = useTranslation()
  // 是否西研环境
  const { useXIYANRag } = useProviderContext()
  // 是否是工作流模式
  const isWorkflow = scene === 'workflow'
  // 是否为无效知识库
  const isFork = config.fork
  // 适配已删除的知识库信息
  const allDatasets = useMemo(() => {
    return config.datasets?.datasets.map((item) => {
      const findDataset = dataSets.find(dataset => dataset.id === item.id || (dataset.old_id === item.id))
      if (findDataset)
        return { ...findDataset, lack: false }
      else
        return { ...item, lack: true }
    }) || []
  }, [config.datasets?.datasets, dataSets])
  const featuresStore = useFeaturesStore()

  // 是否存在知识库数据
  const hasData = allDatasets.length > 0
  const defaultConfig: XiyanRetrievalConfig = {
    enabled: true,
    search_method: RETRIEVE_METHOD.hybrid,
    reranking_enable: true,
    reranking_mode: RerankingModeEnum.WeightedScore,
    reranking_model: {
      reranking_provider_name: 'tong_yi',
      reranking_model_name: 'gte-rerank',
    },
    weights: {
      vector_setting: {
        vector_weight: 0.7,
        embedding_provider_name: '',
        embedding_model_name: '',
      },
      keyword_setting: {
        keyword_weight: 0.3,
      },
    },
    top_k: 3,
    score_threshold_enabled: true,
    score_threshold: 0.5,
  }

  // 西研检索配置
  const [xiyanRetrievalConfig, setXiyanRetrievalConfig] = useState<XiyanRetrievalConfig>(defaultConfig)
  const retrievalMethodConfigXiyanRef = useRef<{ resetConfig: (value: XiyanRetrievalConfig) => void }>(null)
  const retrievalMethodConfigRef = useRef<{ resetConfig: (value: RetrievalConfig) => void }>(null)
  const { appDetail } = useStore(
    useShallow(state => ({
      appDetail: state.appDetail,
    })),
  )
  useEffect(() => {
    // 当 appDetail 存在且包含 new_retrieval_model 时
    if (appDetail?.new_retrieval_model && useXIYANRag) {
      const retrieval_model = appDetail.new_retrieval_model.retrieval_model
      console.log('retrieval_model', retrieval_model)

      // 构建新的检索配置
      const newConfig: XiyanRetrievalConfig = {
        enabled: appDetail.new_retrieval_model.enabled,
        search_method: retrieval_model.search_method,
        reranking_enable: retrieval_model.reranking_enable ?? true,
        reranking_mode: retrieval_model.reranking_mode,
        reranking_model: {
          reranking_provider_name: retrieval_model.reranking_model?.provider || '',
          reranking_model_name: retrieval_model.reranking_model?.model || '',
        },
        weights: {
          vector_setting: {
            vector_weight: retrieval_model.weights?.vector_setting?.vector_weight ?? 0.7,
            embedding_provider_name: retrieval_model.weights?.vector_setting?.embedding_provider_name || '',
            embedding_model_name: retrieval_model.weights?.vector_setting?.embedding_model_name || '',
          },
          keyword_setting: {
            keyword_weight: retrieval_model.weights?.keyword_setting?.keyword_weight ?? 0.3,
          },
        },
        top_k: retrieval_model.top_k ?? 3,
        score_threshold_enabled: true,
        score_threshold: retrieval_model.score_threshold ?? 0.5,
      }

      setXiyanRetrievalConfig(newConfig)
      if (retrievalMethodConfigXiyanRef.current)
        retrievalMethodConfigXiyanRef.current.resetConfig(newConfig)
    }
  }, [appDetail, useXIYANRag]) // 添加 useXIYANRag 依赖

  const defaultRetrieverResource: RetrieverResource = {
    enabled: true,
    retrieval_model: {
      top_k: defaultConfig.top_k,
      score_threshold: defaultConfig.score_threshold,
      reranking_model: defaultConfig.reranking_model && {
        provider: defaultConfig.reranking_model.reranking_provider_name || '',
        model: defaultConfig.reranking_model.reranking_model_name || '',
      },
      reranking_mode: defaultConfig.reranking_mode,
      weights: defaultConfig.weights && {
        vector_setting: {
          vector_weight: defaultConfig.weights.vector_setting?.vector_weight || 0.7,
          embedding_provider_name: defaultConfig.weights.vector_setting?.embedding_provider_name || '',
          embedding_model_name: defaultConfig.weights.vector_setting?.embedding_model_name || '',
        },
        keyword_setting: {
          keyword_weight: defaultConfig.weights.keyword_setting?.keyword_weight || 0.3,
        },
      },
      reranking_enable: defaultConfig.reranking_enable,
    },
  }

  // 弹窗打开状态
  const [open, setOpen] = useState(false)
  // 是否打开
  const mergedOpen = open

  // 变更弹窗打开
  const handleOpen = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen)
    },
    [],
  )

  // 取消保存检索配置
  const handleCancel = () => {
    // 判断是否为西研环境
    // if (useXIYANRag) {
    //   retrievalMethodConfigXiyanRef.current?.resetConfig(xiyanRetrievalConfig)
    // }
    // else {
    //   setXiyanRetrievalConfig(cloneDeep(xiyanRetrievalConfig))
    //   retrievalMethodConfigRef.current?.resetConfig(xiyanRetrievalConfig)
    // }
    setXiyanRetrievalConfig(cloneDeep(xiyanRetrievalConfig))
    retrievalMethodConfigRef.current?.resetConfig(xiyanRetrievalConfig)
    handleOpen(false)
  }

  // 保存检索配置的处理函数
  const handleSave = () => {
    // 判断是否为西研环境
    if (useXIYANRag) {
      // 构建西研环境的检索模型配置
      const retrieval_model: RetrievalModelConfig = {
        // 设置搜索方法为混合模式
        search_method: xiyanRetrievalConfig.search_method as RETRIEVE_METHOD,
        // 设置返回结果数量
        top_k: xiyanRetrievalConfig.top_k,
        // 设置是否启用分数阈值
        score_threshold_enabled: xiyanRetrievalConfig.score_threshold_enabled,
        // 设置分数阈值,如果未设置则默认为0
        score_threshold: xiyanRetrievalConfig.score_threshold || 0,
        // 配置重排序模型信息
        reranking_model: {
          reranking_provider_name: xiyanRetrievalConfig.reranking_model.reranking_provider_name,
          reranking_model_name: xiyanRetrievalConfig.reranking_model.reranking_model_name,
        },
        // 设置重排序模式
        reranking_mode: xiyanRetrievalConfig.reranking_mode,
        // 配置权重设置,如果存在权重配置则进行设置
        weights: xiyanRetrievalConfig.weights && {
          // 配置向量设置
          vector_setting: {
            // 设置向量权重
            vector_weight: xiyanRetrievalConfig.weights.vector_setting.vector_weight,
            // 设置嵌入提供商名称,如果未设置则为空字符串
            embedding_provider_name: xiyanRetrievalConfig.weights.vector_setting.embedding_provider_name || '',
            // 设置嵌入模型名称,如果未设置则为空字符串
            embedding_model_name: xiyanRetrievalConfig.weights.vector_setting.embedding_model_name || '',
          },
          // 配置关键词设置
          keyword_setting: {
            // 设置关键词权重
            keyword_weight: xiyanRetrievalConfig.weights.keyword_setting.keyword_weight,
          },
        },
        // 设置是否启用重排序
        reranking_enable: xiyanRetrievalConfig.reranking_enable,
      }

      // 输出检索模型配置到控制台
      console.log('retrieval_model', retrieval_model)

      // 如果存在特性存储,则更新特性存储中的配置
      if (featuresStore) {
        const features = featuresStore.getState().features
        // 构建符合类型定义的检索配置
        const citationRetrievalModel: RetrieverResource = {
          enabled: true,
          retrieval_model: {
            search_method: retrieval_model.search_method,
            top_k: retrieval_model.top_k,
            score_threshold: retrieval_model.score_threshold,
            reranking_model: retrieval_model.reranking_model && {
              provider: retrieval_model.reranking_model.reranking_provider_name,
              model: retrieval_model.reranking_model.reranking_model_name,
            },
            reranking_mode: retrieval_model.reranking_mode,
            weights: retrieval_model.weights && {
              vector_setting: {
                vector_weight: retrieval_model.weights.vector_setting.vector_weight,
                embedding_provider_name: retrieval_model.weights.vector_setting.embedding_provider_name || '',
                embedding_model_name: retrieval_model.weights.vector_setting.embedding_model_name || '',
              },
              keyword_setting: {
                keyword_weight: retrieval_model.weights.keyword_setting.keyword_weight,
              },
            },
            reranking_enable: retrieval_model.reranking_enable,
          },
        }

        // 更新特性存储中的配置
        console.log('features', features)
        console.log('citationRetrievalModel', citationRetrievalModel)
        featuresStore.setState({
          features: {
            ...features,
            citation: {
              ...features.citation,
              enabled: true,
              retrieval_model: citationRetrievalModel.retrieval_model,
            },
            new_retrieval_model: useXIYANRag ? citationRetrievalModel : defaultRetrieverResource,
          },
        })
      }
      // // 调用回调函数更新检索配置
      // onMultipleRetrievalConfigChange(retrieval_model)
    }

    // 关闭配置面板
    handleOpen(false)
  }

  const renderRight = () => {
    if (useXIYANRag) {
      return <>
        <TextButton
          variant='hover'
          onClick={() => {
            handleOpen(true)
          }}
          disabled={!hasData}
        >
          <RiEqualizer2Line className='mr-1 w-3.5 h-3.5' />
          {t('dataset.action.hitSettings')}
        </TextButton>
        <Modal
          title={t('dataset.action.hitSettings')}
          isShow={mergedOpen}
          onClose={handleCancel}
          closable
          footer={(
            <div className='flex items-center justify-end space-x-4'>
              <Button
                onClick={handleCancel}
                variant={'secondary-accent'}
                className='mr-4'
              >
                {t('common.operation.cancel')}
              </Button>
              <Button
                variant='primary'
                onClick={handleSave}
              >
                {t('common.operation.save')}
              </Button>
            </div>
          )}
        >
          <div className='pb-[24px]'>
            <RetrievalMethodConfigXiyan
              ref={retrievalMethodConfigXiyanRef}
              value={xiyanRetrievalConfig}
              onChange={setXiyanRetrievalConfig}
              isInApp={isInApp}
            />
          </div>
        </Modal>
      </>
    }
    else {
      return <div>
        <ParamsConfig config={config} onChange={onConfig} disabled={!hasData} selectedDatasets={dataSets} />
      </div>
    }
  }

  return (
    <>
      <FeatureCollapse
        title={t('dataset.title')}
        tooltip={t('dataset.notify.addTip')}
        headerRight={
          <>
            <div className='flex items-center gap-2'>
              {<ParamsConfig config={config} onChange={onConfig} disabled={!hasData} selectedDatasets={dataSets} />}
              {/* {renderRight()} */}
              <Divider type='vertical' className='!mx-0'></Divider>
              {<Add onClick={onAdd} className='text-[#5C6273] cursor-pointer'></Add>}
            </div>
          </>
        }
        useSwitch={false}
        inWorkflow={isWorkflow}
      >
        {forkDatasets.length > 0 && isFork && (
          <ForkTip
            className='forkTip mx-4 mb-2'
            message={t('common.fork.datasetTip')}
            onClose={onCloseFock}
          >
            <div>
              {forkDatasets.map(({ id, name }) => (
                <div key={id} className=''>
                  <div className=''>
                        · {name}
                  </div>
                </div>
              ))}
            </div>
          </ForkTip>
        )}
        {hasData && (
          <div className='flex flex-wrap justify-between'>
            {(allDatasets as DataSet[]).map(item => (
              <DatasetItem
                key={item.id}
                name={item.name}
                id={item.id}
                lack={item.lack}
                operations={
                  <>
                    {/* 工作流大模型和智能体知识库编辑按钮 */}
                    { !item.lack
                    && <TextButton onClick={() => onSetting(item)} variant='text'>
                      <Edit className='w-4 h-4' />
                    </TextButton>
                    }
                    <TextButton variant='text' onClick={() => onRemove(item.id)}>
                      <Delete className='w-4 h-4' />
                    </TextButton>
                  </>
                }
                border={border}
              ></DatasetItem>
            ))}
          </div>
        )}
      </FeatureCollapse>
    </>
  )
})
AddDatasetPanelConetnt.displayName = 'AddDatasetPanelConetnt'

const AddDatasetPanel = ({
  dataSets,
  config,
  scene,
  onRemove,
  onSelect,
  onConfig,
}: AddDatasetPanelProps) => {
  // 是否显示选择知识库弹窗
  const [showSelectDataSet, setShowSelectDataSet] = useState(false)
  // 是否显示设置知识库弹窗
  const [showSettingDataSet, setShowSettingDataSet] = useState(false)
  // 选中的知识库
  const [selectDataset, setSelectDataset] = useState<DataSet>()
  // 知识库列表
  const [datasetList, setDatasetList] = useState<DataSet[]>([])
  // 无效知识库
  const [forkDatasets, setForkDatasets] = useState<DataSet[]>([])

  // 是否为无效知识库
  const isFork = config.fork

  // 编辑知识库列表
  const handleEditDataset = (dataSet: DataSet) => {
    const newInputs = produce(datasetList, (draft) => {
      const index = draft.findIndex(item => item.id === dataSet.id)
      draft[index] = dataSet
    })
    setDatasetList(newInputs)
    setShowSettingDataSet(false)
  }
  // 删除知识库列表
  const handleRemoveDataset = (id: string) => {
    const newInputs = produce(datasetList, (draft) => {
      const index = draft.findIndex(item => item.id === id)
      draft.splice(index, 1)
    })
    setDatasetList(newInputs)
    onRemove(id)
  }
  // 添加知识库
  const handleAddDataset = (value: DataSet[]) => {
    setDatasetList(value)
    onSelect(value)
  }

  useEffect(() => {
    if (isFork)
      setForkDatasets(dataSets)
    else
      setDatasetList(dataSets)
  }, [dataSets, isFork])

  return (
    <>
      <AddDatasetPanelConetnt
        dataSets={datasetList}
        forkDatasets={forkDatasets}
        config={config}
        scene={scene}
        onRemove={handleRemoveDataset}
        onConfig={onConfig}
        onCloseFock={() => onSelect([])}
        onAdd={() => setShowSelectDataSet(true)}
        onSetting={(value) => {
          setSelectDataset(value)
          setShowSettingDataSet(true)
        }}
        // onMultipleRetrievalConfigChange={(config) => {
        //   // This function is called when the retrieval config changes in the modal.
        //   // It needs to be implemented to update the parent's config state.
        //   // For now, we'll just log the change.
        //   console.log('Retrieval config changed:', config)
        //   // Example: If you have a state for config, you might update it here.
        //   // setConfig(prev => ({ ...prev, retrieval_model: config }));
        // }}
      ></AddDatasetPanelConetnt>
      {/* 选择知识库弹窗 */}
      {
        showSelectDataSet && <AddDatasetModal
          isShow={true}
          value={datasetList}
          onClose={() => setShowSelectDataSet(false)}
          onSelect={handleAddDataset}
        ></AddDatasetModal>
      }
      {/* 编辑知识库弹窗 */}
      {
        showSettingDataSet && <SettingsModal
          onSave={handleEditDataset}
          onCancel={() => setShowSettingDataSet(false)}
          isShowSettingsModal={true}
          currentDataset={selectDataset!}
        ></SettingsModal>
      }
    </>
  )
}
export default React.memo(AddDatasetPanel)
