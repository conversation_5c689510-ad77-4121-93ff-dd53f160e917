'use client'
import type { FC } from 'react'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { RiEqualizer2Line } from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import { cloneDeep } from 'lodash-es'
import type { MultipleRetrievalConfig, RetrievalModelConfig, SingleRetrievalConfig } from '../types'
import { RETRIEVE_METHOD, RETRIEVE_TYPE, RerankingModeEnum } from '@/types/datasets'
import { DATASET_DEFAULT } from '@/config'
import type { DatasetConfigs } from '@/models/debug'
import type { DataSet } from '@/models/datasets'
import type { ModelConfig } from '@/app/components/workflow/types'

// 知识库公共能力
import ConfigRetrievalContent from '@/app/components/datasets/common/add-dataset-panel/params-config/config-content'
// 账户设置公共能力
import { useModelListAndDefaultModelAndCurrentProviderAndModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
// 西研知识库配置
// 引入设置弹窗组件
import SettingsModal from '@/app/components/datasets/common/add-dataset-panel/setting-modal'

// G公共能力
import cn from '@/utils/classnames'
import {
  PortalToFollowElem,
  PortalToFollowElemContent,
  PortalToFollowElemTrigger,
} from '@/app/components/base/portal-to-follow-elem'
import Button from '@/app/components/base/button'
import { useProviderContext } from '@/context/provider-context'
import { useFeaturesStore } from '@/app/components/base/features/hooks'

type Props = {
  readonly?: boolean
  openFromProps?: boolean
  onOpenFromPropsChange?: (openFromProps: boolean) => void
  // 检索配置
  payload: {
    retrieval_mode: RETRIEVE_TYPE
    multiple_retrieval_config?: MultipleRetrievalConfig
    single_retrieval_config?: SingleRetrievalConfig
  }
  // 模型配置
  singleRetrievalModelConfig?: ModelConfig
  // 选择的知识库
  selectedDatasets: DataSet[]
  // 检索配置变化
  onMultipleRetrievalConfigChange: (config: MultipleRetrievalConfig) => void
  // 检索模式变化
  onRetrievalModeChange: (mode: RETRIEVE_TYPE) => void
  // 模型变化
  onSingleRetrievalModelChange?: (config: ModelConfig) => void
  onSingleRetrievalModelParamsChange?: (config: ModelConfig) => void
  // 控制使用哪个组件
  useSettingsModal?: boolean
}

const defaultConfig = {
  enabled: true,
  search_method: RETRIEVE_METHOD.hybrid,
  reranking_enable: true,
  reranking_mode: RerankingModeEnum.WeightedScore,
  reranking_model: {
    reranking_provider_name: 'tong_yi',
    reranking_model_name: 'gte-rerank',
  },
  weights: {
    vector_setting: {
      vector_weight: 0.7,
      embedding_provider_name: '',
      embedding_model_name: '',
    },
    keyword_setting: {
      keyword_weight: 0.3,
    },
  },
  top_k: 3,
  score_threshold_enabled: true,
  score_threshold: 0.5,
} satisfies Required<RetrievalModelConfig>

const RetrievalConfigComponent: FC<Props> = ({
  payload,
  onRetrievalModeChange,
  onMultipleRetrievalConfigChange,
  singleRetrievalModelConfig,
  readonly,
  openFromProps,
  onOpenFromPropsChange,
  selectedDatasets,
  onSingleRetrievalModelChange,
  onSingleRetrievalModelParamsChange,
  useSettingsModal = true,
}) => {
  const { t } = useTranslation()
  const { defaultModel: rerankDefaultModel, isModelListLoading: isRerankDefaultModelValid, modelList: rerankModelList }
    = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.rerank)
  const { multiple_retrieval_config } = payload
  const featuresStore = useFeaturesStore()

  // 弹窗打开状态
  const [open, setOpen] = useState(false)
  // 是否打开
  const mergedOpen = openFromProps !== undefined ? openFromProps : open
  // 是否西研环境
  const { useXIYANRag } = useProviderContext()

  // 检索配置
  const [retrievalConfig, setRetrievalConfig] = useState<RetrievalModelConfig>(defaultConfig)
  const [tempRetrievalConfig, setTempRetrievalConfig] = useState<RetrievalModelConfig>(defaultConfig)

  // 西研检索配置
  const [xiyanRetrievalConfig, setXiyanRetrievalConfig] = useState<Required<RetrievalModelConfig>>(defaultConfig)

  const retrievalMethodConfigXiyanRef = useRef<{ resetConfig: (value: RetrievalModelConfig) => void }>(null)
  const retrievalMethodConfigRef = useRef<{ resetConfig: (value: RetrievalModelConfig) => void }>(null)

  // 如果没有选中的数据集，使用默认空数据集
  const currentDataset = selectedDatasets.length > 0 ? selectedDatasets[0] : ({} as DataSet)

  // 初始化检索配置
  useEffect(() => {
    if (multiple_retrieval_config && useXIYANRag) {
      // 构建基础检索配置对象
      const config: RetrievalModelConfig = {
        // 设置搜索方法为混合模式
        search_method: (multiple_retrieval_config.search_method as RETRIEVE_METHOD) || RETRIEVE_METHOD.hybrid,
        // 设置是否启用重排序,如果未设置则默认为false
        reranking_enable: multiple_retrieval_config.reranking_enable || false,
        // 配置重排序模型信息
        reranking_model: {
          // 设置重排序提供商名称,如果未设置则为空字符串
          reranking_provider_name: multiple_retrieval_config.reranking_model?.reranking_provider_name || '',
          // 设置重排序模型名称,如果未设置则为空字符串
          reranking_model_name: multiple_retrieval_config.reranking_model?.reranking_model_name || '',
        },
        // 设置返回结果数量
        top_k: multiple_retrieval_config.top_k,
        // 启用分数阈值
        score_threshold_enabled: true,
        // 设置分数阈值,如果未设置则默认为0
        score_threshold: multiple_retrieval_config.score_threshold || 0,
        // 设置重排序模式
        reranking_mode: multiple_retrieval_config.reranking_mode || RerankingModeEnum.WeightedScore,
        // 配置权重设置
        weights: {
          vector_setting: {
            vector_weight: multiple_retrieval_config.weights?.vector_setting?.vector_weight ?? 0.7,
            embedding_provider_name: multiple_retrieval_config.weights?.vector_setting?.embedding_provider_name ?? '',
            embedding_model_name: multiple_retrieval_config.weights?.vector_setting?.embedding_model_name ?? '',
          },
          keyword_setting: {
            keyword_weight: multiple_retrieval_config.weights?.keyword_setting?.keyword_weight ?? 0.3,
          },
        } as RetrievalModelConfig['weights'],
      }
      // 更新检索配置状态
      setRetrievalConfig(config)
      // 创建临时配置的深拷贝
      setTempRetrievalConfig(cloneDeep(config))
    }
  }, [multiple_retrieval_config, useXIYANRag])

  // 变更弹窗打开
  const handleOpen = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen)
      onOpenFromPropsChange?.(newOpen)
    },
    [onOpenFromPropsChange],
  )

  // 处理SettingsModal保存
  const handleSaveSettings = useCallback(
    (newDataset: DataSet) => {
      // 将SettingsModal的保存结果转换为适合当前组件的格式
      console.log('handleSaveSettings newDataset:', newDataset)
      if (newDataset.retrieval_model_dict) {
        const retrieval_model = newDataset.retrieval_model_dict as unknown as any
        console.log('handleSaveSettings retrieval_model:', retrieval_model)
        onMultipleRetrievalConfigChange(retrieval_model)
      }
      else {
        console.warn('retrieval_model_dict is empty')
      }
      handleOpen(false)
    },
    [onMultipleRetrievalConfigChange, handleOpen],
  )

  // 变更知识库配置
  const handleChange = useCallback(
    (configs: DatasetConfigs, isRetrievalModeChange?: boolean) => {
      if (isRetrievalModeChange) {
        onRetrievalModeChange(configs.retrieval_model)
        return
      }
      const retrieval_model: RetrievalModelConfig = {
        search_method: (configs.search_method as RETRIEVE_METHOD) || RETRIEVE_METHOD.hybrid,
        score_threshold_enabled: true,
        top_k: configs.top_k,
        score_threshold: configs.score_threshold_enabled
          ? configs.score_threshold ?? DATASET_DEFAULT.score_threshold
          : 0,
        reranking_model: {
          reranking_provider_name: payload.retrieval_mode === RETRIEVE_TYPE.oneWay
            ? ''
            : !configs.reranking_model?.reranking_provider_name
              ? (rerankDefaultModel?.provider?.provider || '')
              : configs.reranking_model.reranking_provider_name,
          reranking_model_name: payload.retrieval_mode === RETRIEVE_TYPE.oneWay
            ? ''
            : !configs.reranking_model?.reranking_model_name
              ? (rerankDefaultModel?.model || '')
              : configs.reranking_model.reranking_model_name,
        },
        reranking_mode: configs.reranking_mode || RerankingModeEnum.WeightedScore,
        weights: {
          vector_setting: multiple_retrieval_config?.weights?.vector_setting || {
            vector_weight: 0.7,
            embedding_provider_name: '',
            embedding_model_name: '',
          },
          keyword_setting: multiple_retrieval_config?.weights?.keyword_setting || {
            keyword_weight: 0.3,
          },
        } as RetrievalModelConfig['weights'],
        reranking_enable: configs.reranking_enable || false,
      }
      onMultipleRetrievalConfigChange(retrieval_model)
    },
    [payload.retrieval_mode, rerankDefaultModel?.provider?.provider, rerankDefaultModel?.model, multiple_retrieval_config?.weights?.vector_setting, multiple_retrieval_config?.weights?.keyword_setting, onMultipleRetrievalConfigChange, onRetrievalModeChange],
  )

  // 保存检索配置的处理函数
  const handleSave = () => {
    // 构建西研环境的检索模型配置
    const retrieval_model: RetrievalModelConfig = {
      // 设置搜索方法为混合模式
      search_method: xiyanRetrievalConfig.search_method || 'hybrid_search',
      // 设置返回结果数量
      top_k: xiyanRetrievalConfig.top_k,
      // 设置是否启用分数阈值
      score_threshold_enabled: xiyanRetrievalConfig.score_threshold_enabled,
      // 设置分数阈值,如果未设置则默认为0
      score_threshold: xiyanRetrievalConfig.score_threshold || 0,
      // 配置重排序模型信息
      reranking_model: {
        reranking_provider_name: xiyanRetrievalConfig.reranking_model.reranking_provider_name,
        reranking_model_name: xiyanRetrievalConfig.reranking_model.reranking_model_name,
      },
      // 设置重排序模式
      reranking_mode: xiyanRetrievalConfig.reranking_mode,
      // 配置权重设置,如果存在权重配置则进行设置
      weights: xiyanRetrievalConfig.weights && {
        // 配置向量设置
        vector_setting: {
          // 设置向量权重
          vector_weight: xiyanRetrievalConfig.weights.vector_setting.vector_weight,
          // 设置嵌入提供商名称,如果未设置则为空字符串
          embedding_provider_name: xiyanRetrievalConfig.weights.vector_setting.embedding_provider_name || '',
          // 设置嵌入模型名称,如果未设置则为空字符串
          embedding_model_name: xiyanRetrievalConfig.weights.vector_setting.embedding_model_name || '',
        },
        // 配置关键词设置
        keyword_setting: {
          // 设置关键词权重
          keyword_weight: xiyanRetrievalConfig.weights.keyword_setting.keyword_weight,
        },
      },
      // 设置是否启用重排序
      reranking_enable: xiyanRetrievalConfig.reranking_enable,
    }

    // 输出检索模型配置到控制台
    console.log('retrieval_model', retrieval_model)

    // 如果存在特性存储,则更新特性存储中的配置
    if (featuresStore) {
      const features = featuresStore.getState().features
      // 构建符合类型定义的检索配置
      const citationRetrievalModel = {
        enabled: features?.citation?.enabled,
        top_k: retrieval_model.top_k,
        score_threshold: retrieval_model.score_threshold,
        reranking_model: retrieval_model.reranking_model && {
          provider: retrieval_model.reranking_model.reranking_provider_name,
          model: retrieval_model.reranking_model.reranking_model_name,
        },
        reranking_mode: retrieval_model.reranking_mode,
        weights: retrieval_model.weights && {
          vector_setting: {
            vector_weight: retrieval_model.weights.vector_setting.vector_weight,
            embedding_provider_name: retrieval_model.weights.vector_setting.embedding_provider_name || '',
            embedding_model_name: retrieval_model.weights.vector_setting.embedding_model_name || '',
          },
          keyword_setting: {
            keyword_weight: retrieval_model.weights.keyword_setting.keyword_weight,
          },
        },
        reranking_enable: retrieval_model.reranking_enable,
      }

      featuresStore.setState({
        features: {
          ...features,
          citation: {
            ...features.citation,
            enabled: features?.citation?.enabled,
            retrieval_model: citationRetrievalModel,
          },
          new_retrieval_model: citationRetrievalModel,
        },
      })
      console.log('retrieval-config features', features)
    }
    // 调用回调函数更新检索配置
    onMultipleRetrievalConfigChange(retrieval_model)
    // 关闭配置面板
    handleOpen(false)
  }

  // 取消保存检索配置
  const handleCancel = () => {
    // if (useXIYANRag) {
    //   retrievalMethodConfigXiyanRef.current?.resetConfig(xiyanRetrievalConfig)
    // }
    // else {
    //   setTempRetrievalConfig(cloneDeep(retrievalConfig))
    //   retrievalMethodConfigRef.current?.resetConfig(retrievalConfig)
    // }
    setTempRetrievalConfig(cloneDeep(retrievalConfig))
    retrievalMethodConfigRef.current?.resetConfig(retrievalConfig)
    handleOpen(false)
  }

  return (
    <>
      {!useSettingsModal
        ? (
          <PortalToFollowElem
            open={mergedOpen}
            onOpenChange={handleOpen}
            placement="bottom-end"
            offset={{
              crossAxis: -2,
            }}
          >
            <PortalToFollowElemTrigger
              onClick={() => {
                if (readonly)
                  return
                handleOpen(!mergedOpen)
              }}
            >
              <Button
                variant="ghost"
                size="small"
                disabled={readonly}
                className={cn(open && 'bg-gray-G5')}
              >
                <RiEqualizer2Line className="mr-1 w-3.5 h-3.5" />
                {t('dataset.action.retrievalSettings')}
              </Button>
            </PortalToFollowElemTrigger>
            <PortalToFollowElemContent style={{ zIndex: 1001 }}>
              <div className="w-[404px] pt-3 pb-4 px-4 shadow-xl rounded-2xl border border-gray-200 bg-white">
                <ConfigRetrievalContent
                  datasetConfigs={{
                    retrieval_model: payload.retrieval_mode,
                    reranking_model: multiple_retrieval_config?.reranking_model?.reranking_provider_name
                      ? {
                        reranking_provider_name: multiple_retrieval_config.reranking_model.reranking_provider_name,
                        reranking_model_name: multiple_retrieval_config.reranking_model.reranking_model_name,
                      }
                      : {
                        reranking_provider_name: '',
                        reranking_model_name: '',
                      },
                    top_k: multiple_retrieval_config?.top_k || DATASET_DEFAULT.top_k,
                    score_threshold_enabled: !(
                      multiple_retrieval_config?.score_threshold === undefined
                    || multiple_retrieval_config.score_threshold === null
                    ),
                    score_threshold: multiple_retrieval_config?.score_threshold,
                    datasets: {
                      datasets: [],
                    },
                    reranking_mode: multiple_retrieval_config?.reranking_mode,
                    weights: {
                      vector_setting: {
                        vector_weight: 0.7,
                        embedding_provider_name: '',
                        embedding_model_name: '',
                      },
                      keyword_setting: {
                        keyword_weight: 0.3,
                      },
                    } as RetrievalModelConfig['weights'],
                    reranking_enable: multiple_retrieval_config?.reranking_enable,
                  }}
                  onChange={handleChange}
                  isInWorkflow
                  singleRetrievalModelConfig={singleRetrievalModelConfig}
                  onSingleRetrievalModelChange={onSingleRetrievalModelChange}
                  onSingleRetrievalModelParamsChange={onSingleRetrievalModelParamsChange}
                  selectedDatasets={selectedDatasets}
                />
              </div>
            </PortalToFollowElemContent>
          </PortalToFollowElem>
        )
        : (
          <>
            <Button
              variant="ghost"
              size="small"
              disabled={readonly}
              className={cn(mergedOpen && 'bg-gray-G5')}
              onClick={() => {
                if (readonly)
                  return
                handleOpen(!mergedOpen)
              }}
            >
              <RiEqualizer2Line className="mr-1 w-3.5 h-3.5" />
              {t('dataset.action.retrievalSettings')}
              {/* {useXIYANRag ? t('dataset.action.hitSettings') : t('dataset.action.retrievalSettings')} */}
            </Button>

            {mergedOpen && (
              // useXIYANRag
              //   ? (
              //     <Modal
              //       title={t('dataset.action.hitSettings')}
              //       isShow={mergedOpen}
              //       onClose={handleCancel}
              //       closable
              //       footer={(
              //         <div className='flex items-center justify-end space-x-4'>
              //           <Button
              //             onClick={handleCancel}
              //             variant={'secondary-accent'}
              //             className='mr-4'
              //           >
              //             {t('common.operation.cancel')}
              //           </Button>
              //           <Button
              //             variant='primary'
              //             onClick={handleSave}
              //           >
              //             {t('common.operation.save')}
              //           </Button>
              //         </div>
              //       )}
              //     >
              //       <div className='pb-[24px]'>
              //         <RetrievalMethodConfigXiyan
              //           ref={retrievalMethodConfigXiyanRef}
              //           disabled={readonly}
              //           value={xiyanRetrievalConfig}
              //           onChange={setXiyanRetrievalConfig}
              //           isInApp={true} // 工作流知识库命中设置
              //         />
              //       </div>
              //     </Modal>
              //   )
              //   :
            // (
              <SettingsModal
                isShowSettingsModal={mergedOpen}
                currentDataset={{
                  ...currentDataset,
                  id: currentDataset.id || '',
                  name: currentDataset.name || '',
                  description: currentDataset.description || '',
                  permission: currentDataset.permission || 'only_me',
                  embedding_available: true,
                  indexing_technique: currentDataset.indexing_technique || 'high_quality',
                  retrieval_model_dict: multiple_retrieval_config as unknown as any,
                }}
                onCancel={handleCancel}
                onSave={handleSaveSettings}
              />
            // )
            )}
          </>
        )}
    </>
  )
}

export default React.memo(RetrievalConfigComponent)
