'use client'
import type { FC } from 'react'
import React, { useCallback, useState } from 'react'
import produce from 'immer'
import { useTranslation } from 'react-i18next'
import type { DataSet } from '@/models/datasets'
import DatasetItem from '@/app/components/datasets/common/dataset-item'
import TextButton from '@/app/components/base/button/text-button'
import { Delete, Edit } from '@/app/components/base/icons/src/vender/line/general'
import SettingsModal from '@/app/components/datasets/common/add-dataset-panel/setting-modal'
import { useProviderContext } from '@/context/provider-context'

type Props = {
  list: DataSet[]
  showEmpty?: boolean
  onChange: (list: DataSet[]) => void
  readonly?: boolean
}

const DatasetList: FC<Props> = ({
  list,
  showEmpty = true,
  onChange,
  readonly,
}) => {
  const { t } = useTranslation()

  // 是否显示设置知识库弹窗
  const [showSettingDataSet, setShowSettingDataSet] = useState(false)
  // 选中的知识库
  const [selectDataset, setSelectDataset] = useState<DataSet>()
  // 选中的知识库索引
  const [selectIndex, setSelectIndex] = useState<number>(-1)
  // 删除知识库
  const handleRemove = useCallback((index: number) => {
    const newList = produce(list, (draft) => {
      draft.splice(index, 1)
    })
    onChange(newList)
  }, [list, onChange])
  // 是否西研环境
  const { useXIYANRag } = useProviderContext()

  // 变更知识库
  const handleChange = useCallback((value: DataSet) => {
    const newList = produce(list, (draft) => {
      draft[selectIndex] = value
    })
    onChange(newList)
    setShowSettingDataSet(false)
  }, [list, onChange, selectIndex])
  if (!showEmpty && list.length === 0)
    return null
  return (
    <>
      <div className='space-y-1'>
        {list.length
          ? list.map((item, index) => {
            return (
              <DatasetItem
                key={index}
                name={item.name}
                id={item.id}
                lack={item.lack}
                border
                operations={
                  readonly
                    ? <></>
                    : <>
                      {/* 工作流知识库编辑按钮 */}
                      { !item.lack
                  && <TextButton onClick={() => {
                    setSelectDataset(item)
                    setSelectIndex(index)
                    setShowSettingDataSet(true)
                  }} variant='text'>
                    <Edit className='w-4 h-4' />
                  </TextButton>
                      }
                      <TextButton variant='text' onClick={() => handleRemove(index)}>
                        <Delete className='w-4 h-4' />
                      </TextButton>
                    </>
                }
              />
            )
          })
          : (
            <div className='p-3 text-xs text-center text-gray-500 rounded-lg cursor-default select-none bg-gray-50'>
              {t('dataset.notify.knowledgeTip')}
            </div>
          )
        }

      </div>
      {/* 编辑知识库弹窗 */}
      {
        showSettingDataSet && <SettingsModal
          onSave={handleChange}
          onCancel={() => setShowSettingDataSet(false)}
          isShowSettingsModal={true}
          currentDataset={selectDataset!}
        ></SettingsModal>
      }
    </>
  )
}
export default React.memo(DatasetList)
