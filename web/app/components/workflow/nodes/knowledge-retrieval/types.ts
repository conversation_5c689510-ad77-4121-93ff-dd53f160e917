import type { CommonNodeType, ModelConfig, ValueSelector } from '@/app/components/workflow/types'
import type { RETRIEVE_METHOD, RETRIEVE_TYPE, RerankingModeEnum } from '@/types/datasets'

export type RetrievalModelConfig = {
  enabled?: boolean
  search_method: RETRIEVE_METHOD
  reranking_enable: boolean
  reranking_model: {
    reranking_provider_name: string
    reranking_model_name: string
  }
  top_k: number
  score_threshold_enabled: boolean
  score_threshold: number
  reranking_mode: RerankingModeEnum
  weights: {
    vector_setting: {
      vector_weight: number
      embedding_provider_name: string
      embedding_model_name: string
    }
    keyword_setting: {
      keyword_weight: number
    }
  }
}

export type MultipleRetrievalConfig = {
  search_method?: RETRIEVE_METHOD
  top_k: number
  score_threshold: number | null | undefined
  reranking_model?: {
    reranking_provider_name: string
    reranking_model_name: string
  }
  reranking_mode?: RerankingModeEnum
  weights?: {
    vector_setting: {
      vector_weight: number
      embedding_provider_name: string
      embedding_model_name: string
    }
    keyword_setting: {
      keyword_weight: number
    }
  }
  reranking_enable?: boolean
}

export type SingleRetrievalConfig = {
  model: ModelConfig
}

export type KnowledgeRetrievalNodeType = CommonNodeType & {
  query_variable_selector: ValueSelector
  dataset_ids: string[]
  retrieval_mode: RETRIEVE_TYPE
  multiple_retrieval_config?: MultipleRetrievalConfig
  single_retrieval_config?: SingleRetrievalConfig
  fork?: boolean
  lack?: boolean
  new_retrieval_model?: RetrievalModelConfig
}

export type RetrievalConfig = {
  retrieval_mode: RETRIEVE_TYPE
  multiple_retrieval_config?: MultipleRetrievalConfig
  single_retrieval_config?: SingleRetrievalConfig
}
