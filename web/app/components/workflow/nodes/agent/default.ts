import { Agent<PERSON>reateMode, type AgentNodeType } from './types'
import { RET<PERSON>EVE_METHOD, RETRIEVE_TYPE, RerankingModeEnum } from '@/types/datasets'
import { DATASET_DEFAULT } from '@/config'

// 工作流公共能力
import { BlockEnum, EditionType, TextVarFormat } from '@/app/components/workflow/types'
import { type NodeDefault, type PromptItem, PromptRole } from '@/app/components/workflow/types'
import { ALL_CHAT_AVAILABLE_BLOCKS, ALL_COMPLETION_AVAILABLE_BLOCKS } from '@/app/components/workflow/constants/index'
import { DEFAULT_WEIGHTED_SCORE } from '@/config/dataset'

const i18nPrefix = 'workflow.errorMsg'

const nodeDefault: NodeDefault<AgentNodeType> = {
  defaultValue: {
    create_mode: AgentCreateMode.Empty,
    agent_desc: '',
    agent_id: '',
    site_app_id: '',
    // 空态下模型信息
    model: {
      provider: '',
      name: '',
      mode: 'chat',
      completion_params: {
        temperature: 0.7,
      },
      outputs: {},
      response_format: TextVarFormat.Markdown,
    },
    // 系统提示词
    prompt_template: [{
      role: PromptRole.system,
      text: '',
      new_level: true,
    }, {
      role: PromptRole.user,
      text: '',
      new_level: true,
    }],
    // 上下文配置
    context: {
      enabled: false,
      variable_selector: [],
    },
    // 视觉配置
    vision: {
      enabled: false,
    },
    // 工具选择
    tools: [],
    // 知识库配置
    dataset_configs: {
      retrieval_model: RETRIEVE_TYPE.multiWay,
      reranking_model: {
        reranking_provider_name: '',
        reranking_model_name: '',
      },
      reranking_mode: RerankingModeEnum.RerankingModel,
      reranking_enable: true,
      weights: {
        vector_setting: {
          vector_weight: DEFAULT_WEIGHTED_SCORE.other.semantic,
          embedding_provider_name: '',
          embedding_model_name: '',
        },
        keyword_setting: {
          keyword_weight: DEFAULT_WEIGHTED_SCORE.other.keyword,
        },
      },
      top_k: DATASET_DEFAULT.top_k,
      score_threshold_enabled: false,
      score_threshold: DATASET_DEFAULT.score_threshold,
      datasets: {
        datasets: [],
      },
    },
    // 新知识库配置
    new_retrieval_model: {
      search_method: RETRIEVE_METHOD.hybrid,
      reranking_enable: false,
      reranking_mode: RerankingModeEnum.WeightedScore,
      reranking_model: {
        reranking_provider_name: '',
        reranking_model_name: '',
      },
      weights: {
        keyword_setting: {
          keyword_weight: 0.3,
        },
        vector_setting: {
          vector_weight: 0.7,
          embedding_provider_name: '',
          embedding_model_name: '',
        },
      },
      top_k: 3,
      score_threshold_enabled: true,
      score_threshold: 0.5,
    },
    // 高级设置
    advance_setting: {
      enabled: false,
      iteration_num: 1,
    },
    query: '{{#sys.query#}}',
    // 工具缺失
    tool_lack: false,
  },
  // 获取上一步有效节点
  getAvailablePrevNodes(isChatMode: boolean) {
    const nodes = isChatMode
      ? ALL_CHAT_AVAILABLE_BLOCKS
      : ALL_COMPLETION_AVAILABLE_BLOCKS.filter(type => type !== BlockEnum.End)
    return nodes
  },
  // 获取下一步有效节点
  getAvailableNextNodes(isChatMode: boolean) {
    const nodes = isChatMode ? ALL_CHAT_AVAILABLE_BLOCKS : ALL_COMPLETION_AVAILABLE_BLOCKS
    return nodes
  },
  // 确认节点是否有效
  checkValid(payload: AgentNodeType, t: any) {
    let errorMessages = ''
    const isEmpty = payload.create_mode === AgentCreateMode.Empty

    // 空态下，知识库缺失
    if (isEmpty && !errorMessages && payload.dataset_configs?.lack)
      errorMessages = t(`${i18nPrefix}.lackDataset`)
    // 空态下，工具缺失
    if (isEmpty && !errorMessages && payload.tool_lack)
      errorMessages = t(`${i18nPrefix}.lackTool`)
    // 空态下，未选择模型
    if (isEmpty && !errorMessages && !payload.model?.provider)
      errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.model`) })
    // 空态下，没有打开记忆，且所有提示词都是空的
    if (isEmpty && !errorMessages && !payload.memory) {
      const isChatModel = payload.model?.mode === 'chat'
      const isPromptEmpty = isChatModel
        ? !(payload.prompt_template as PromptItem[]).some((t) => {
          if (t.edition_type === EditionType.jinja2)
            return t.jinja2_text !== ''

          return t.text !== ''
        })
        : ((payload.prompt_template as PromptItem).edition_type === EditionType.jinja2 ? (payload.prompt_template as PromptItem).jinja2_text === '' : (payload.prompt_template as PromptItem).text === '')
      if (isPromptEmpty)
        errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t('workflow.nodes.llm.prompt') })
    }
    // 空态下，打开记忆，但记忆提示词中不包含sys.query
    if (isEmpty && !errorMessages && !!payload.memory) {
      const isChatModel = payload.model?.mode === 'chat'
      // payload.memory.query_prompt_template not pass is default: {{#sys.query#}}
      if (isChatModel && !!payload.memory.query_prompt_template && !payload.memory.query_prompt_template.includes('{{#sys.query#}}'))
        errorMessages = t('workflow.nodes.llm.sysQueryInUser')
    }
    // 空态下，检查提示词jinja的变量列表
    if (isEmpty && !errorMessages) {
      const isChatModel = payload.model?.mode === 'chat'
      const isShowVars = (() => {
        if (isChatModel)
          return (payload.prompt_template as PromptItem[]).some(item => item.edition_type === EditionType.jinja2)
        return (payload.prompt_template as PromptItem).edition_type === EditionType.jinja2
      })()
      if (isShowVars && payload.prompt_config?.jinja2_variables) {
        payload.prompt_config?.jinja2_variables.forEach((i) => {
          if (!errorMessages && !i.variable)
            errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.variable`) })
          if (!errorMessages && !i.value_selector.length)
            errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.variableValue`) })
        })
      }
    }
    // 空态下，但是没有选择视觉变量
    if (isEmpty && !errorMessages && payload.vision?.enabled && !payload.vision.configs?.variable_selector?.length)
      errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.visionVariable`) })
    // 场景说明
    if (!errorMessages && !payload.scene_desc)
      errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.sceneDesc`) })
    // 输入变量
    if (!isEmpty && !errorMessages && !payload.query)
      errorMessages = t(`${i18nPrefix}.fieldRequired`, { field: t(`${i18nPrefix}.fields.outputVar`) })
    return {
      isValid: !errorMessages,
      errorMessage: errorMessages,
    }
  },
}

export default nodeDefault
