import { useCallback } from 'react'
import produce from 'immer'
import { useTranslation } from 'react-i18next'
import { useStoreApi } from 'reactflow'
import { useParams } from 'next/navigation'
import {
  useStore,
  useWorkflowStore,
} from '../store'
import { BlockEnum } from '../types'
import { useWorkflowUpdate } from '../hooks'
import {
  useNodesReadOnly,
} from './use-workflow'
import { syncWorkflowDraft } from '@/service/workflow'
import { API_PREFIX } from '@/config'
import { saveAppChatBgConfig } from '@/service/apps'
import { useFeaturesStore } from '@/app/components/base/features/hooks'
import { FeatureEnum } from '@/app/components/base/features/types'
import Toast from '@/app/components/base/toast'
import { useProviderContext } from '@/context/provider-context'

export const useNodesSyncDraft = () => {
  const { t } = useTranslation()
  const store = useStoreApi()
  const params = useParams()
  const workflowStore = useWorkflowStore()
  const featuresStore = useFeaturesStore()
  const { getNodesReadOnly } = useNodesReadOnly()
  const { handleRefreshWorkflowDraft } = useWorkflowUpdate()
  const { useXIYANRag } = useProviderContext()

  const defaultConfig = {
    search_method: 'hybrid_search',
    reranking_enable: true,
    reranking_mode: 'weighted_score',
    reranking_model: {
      reranking_provider_name: 'tong_yi',
      reranking_model_name: 'gte-rerank',
    },
    weights: {
      weight_type: 'customized',
      vector_setting: {
        vector_weight: 0.7,
        embedding_provider_name: '',
        embedding_model_name: '',
      },
      keyword_setting: {
        keyword_weight: 0.3,
      },
    },
    top_k: 3,
    score_threshold_enabled: true,
    score_threshold: 0.5,
  }

  const debouncedSyncWorkflowDraft = useStore(s => s.debouncedSyncWorkflowDraft)
  // 工作流保存接口参数
  const getPostParams = useCallback(() => {
    const {
      getNodes,
      edges,
      transform,
    } = store.getState()
    const [x, y, zoom] = transform
    const {
      appId,
      conversationVariables,
      environmentVariables,
      syncWorkflowDraftHash,
    } = workflowStore.getState()

    if (appId) {
      const nodes = getNodes()
      const hasStartNode = nodes.find(node => node.data.type === BlockEnum.Start)

      if (!hasStartNode)
        return

      const features = featuresStore!.getState().features
      console.log('use-nodes-sync-draft features', features)

      // 剔除节点和边的私有变量
      const producedNodes = produce(nodes, (draft) => {
        console.log('use-nodes-sync-draft draft', draft)
        draft.forEach((node) => {
          // 同步大模型节点的知识库检索设置
          if (node.data.type === 'llm' && useXIYANRag) {
            const llmNode = node.data
            // 如果存在 new_retrieval_model,则同步到 dataset_configs 和 new_retrieval_model
            if (features?.new_retrieval_model?.enabled) {
              const retrieval_model = features.new_retrieval_model.retrieval_model
              const newConfig = {
                search_method: retrieval_model?.search_method || 'hybrid_search',
                retrieval_model: 'multiple',
                reranking_enable: retrieval_model?.reranking_enable || false,
                reranking_mode: retrieval_model?.reranking_mode || 'weighted_score',
                reranking_model: {
                  reranking_provider_name: retrieval_model?.reranking_model?.provider || '',
                  reranking_model_name: retrieval_model?.reranking_model?.model || '',
                },
                weights: retrieval_model?.weights && {
                  vector_setting: {
                    vector_weight: retrieval_model.weights.vector_setting?.vector_weight || 0.7,
                    embedding_provider_name: retrieval_model.weights.vector_setting?.embedding_provider_name || '',
                    embedding_model_name: retrieval_model.weights.vector_setting?.embedding_model_name || '',
                  },
                  keyword_setting: {
                    keyword_weight: retrieval_model.weights.keyword_setting?.keyword_weight || 0.3,
                  },
                },
                top_k: retrieval_model?.top_k || 3,
                score_threshold_enabled: true,
                score_threshold: retrieval_model?.score_threshold || 0.5,
              }

              // 同步到 dataset_configs
              if (llmNode.dataset_configs && useXIYANRag) {
                llmNode.dataset_configs = {
                  ...llmNode.dataset_configs,
                  ...newConfig,
                }
              }

              // 同步到 new_retrieval_model
              if (llmNode.new_retrieval_model && useXIYANRag) {
                llmNode.new_retrieval_model = {
                  ...llmNode.new_retrieval_model,
                  ...newConfig,
                }
              }
            }
          }
          // 同步智能体节点的知识库检索设置
          if (node.data.type === 'agent' && useXIYANRag) {
            const agentNode = node.data
            // 如果存在 new_retrieval_model,则同步到 agent_dataset_configs 和 agent_retrieval_model
            if (features?.new_retrieval_model?.enabled) {
              const retrieval_model = features.new_retrieval_model.retrieval_model
              const newConfig = {
                search_method: retrieval_model?.search_method || 'hybrid_search',
                retrieval_model: 'multiple',
                reranking_enable: retrieval_model?.reranking_enable || false,
                reranking_mode: retrieval_model?.reranking_mode || 'weighted_score',
                reranking_model: {
                  reranking_provider_name: retrieval_model?.reranking_model?.provider || '',
                  reranking_model_name: retrieval_model?.reranking_model?.model || '',
                },
                weights: retrieval_model?.weights && {
                  vector_setting: {
                    vector_weight: retrieval_model.weights.vector_setting?.vector_weight || 0.7,
                    embedding_provider_name: retrieval_model.weights.vector_setting?.embedding_provider_name || '',
                    embedding_model_name: retrieval_model.weights.vector_setting?.embedding_model_name || '',
                  },
                  keyword_setting: {
                    keyword_weight: retrieval_model.weights.keyword_setting?.keyword_weight || 0.3,
                  },
                },
                top_k: retrieval_model?.top_k || 3,
                score_threshold_enabled: true,
                score_threshold: retrieval_model?.score_threshold || 0.5,
              }

              // 同步到 agent_dataset_configs
              if (agentNode.agent_dataset_configs) {
                agentNode.agent_dataset_configs = {
                  ...agentNode.agent_dataset_configs,
                  ...newConfig,
                }
              }

              // 同步到 agent_retrieval_model
              if (agentNode.agent_retrieval_model) {
                agentNode.agent_retrieval_model = {
                  ...agentNode.agent_retrieval_model,
                  ...newConfig,
                }
              }
            }
          }
          Object.keys(node.data).forEach((key) => {
            if (key.startsWith('_'))
              delete node.data[key]
          })
        })
      })
      const producedEdges = produce(edges, (draft) => {
        draft.forEach((edge) => {
          Object.keys(edge.data).forEach((key) => {
            if (key.startsWith('_'))
              delete edge.data[key]
          })
        })
      })

      const voice_input_data = features.text2speech?.voice_input || {}
      const voice_conversation_data = features.text2speech?.voice_conversation || {}
      // 工作流
      return {
        url: `/apps/${appId}/workflows/draft`,
        params: {
          graph: {
            nodes: producedNodes,
            edges: producedEdges,
            viewport: {
              x,
              y,
              zoom,
            },
          },
          features: {
            opening_statement: features.opening?.enabled ? (features.opening?.opening_statement || '') : '',
            suggested_questions: features.opening?.enabled ? (features.opening?.suggested_questions || []) : [],
            suggested_questions_after_answer: features.suggested,
            // text_to_speech: features.text2speech,
            text_to_speech: {
              ...features.text2speech,
              voice_input: {
                enabled: voice_input_data.enabled || false,
                language: voice_input_data.enabled ? (voice_input_data.language || '') : '',
                timbre: voice_input_data.enabled ? (voice_input_data.timbre || '') : '',
                auto_play: voice_input_data.enabled ? (voice_input_data.auto_play || false) : false,
              },
              voice_conversation: {
                enabled: voice_conversation_data?.enabled || false,
                language: voice_conversation_data?.enabled ? (voice_conversation_data?.language || '') : '',
                timbre: voice_conversation_data?.enabled ? (voice_conversation_data?.timbre || '') : '',
              },

            },
            speech_to_text: features.speech2text,
            retriever_resource: features.citation,
            sensitive_word_avoidance: features.moderation,
            file_upload: features.file,
          },
          environment_variables: environmentVariables,
          conversation_variables: conversationVariables,
          hash: syncWorkflowDraftHash,
          new_retrieval_model: useXIYANRag ? features.new_retrieval_model : defaultConfig,
        },
      }
    }
  }, [store, featuresStore, workflowStore])
  // web页面关闭时自动保存工作流画布
  const syncWorkflowDraftWhenPageClose = useCallback(() => {
    if (getNodesReadOnly())
      return
    const postParams = getPostParams()

    if (postParams) {
      navigator.sendBeacon(
        `${API_PREFIX}/apps/${params.appId}/workflows/draft?_token=${localStorage.getItem('console_token')}`,
        JSON.stringify(postParams.params),
      )
    }
  }, [getPostParams, params.appId, getNodesReadOnly])
  // 保存工作流画布
  const doSyncWorkflowDraft = useCallback(async (notRefreshWhenSyncError?: boolean) => {
    const bgConfig = featuresStore?.getState().features[FeatureEnum.chatBg]

    if (getNodesReadOnly())
      return
    const postParams = getPostParams()
    const { appId } = workflowStore.getState()
    // 保存背景图配置
    saveAppChatBgConfig({
      appId: appId!,
      body: {
        configs: bgConfig,
      },
    })

    if (useXIYANRag)
      console.log('postParams', postParams)

    if (postParams) {
      const {
        setSyncWorkflowDraftHash,
        setDraftUpdatedAt,
      } = workflowStore.getState()
      try {
        const res = await syncWorkflowDraft(postParams)
        setSyncWorkflowDraftHash(res.hash)
        setDraftUpdatedAt(res.updated_at)
        return { success: true, message: null }
      }
      catch (error: any) {
        if (error && error.json && !error.bodyUsed) {
          const err = await error.json()
          if (err.code === 'draft_workflow_not_sync' && !notRefreshWhenSyncError)
            handleRefreshWorkflowDraft()
          Toast.notify({
            type: 'error',
            message: err?.message || t('common.actionMsg.saveFailed'),
          })
          return { success: false, message: err.message }
        }
      }
    }
  }, [featuresStore, getNodesReadOnly, getPostParams, workflowStore, handleRefreshWorkflowDraft])
  // 处理保存工作流画布 sync: 是否立即保存 ,notRefreshWhenSyncError：是否在同步错误时重新拉取工作流数据
  const handleSyncWorkflowDraft = useCallback((sync?: boolean, notRefreshWhenSyncError?: boolean) => {
    if (getNodesReadOnly())
      return

    if (sync)
      doSyncWorkflowDraft(notRefreshWhenSyncError)
    else
      debouncedSyncWorkflowDraft(doSyncWorkflowDraft)
  }, [debouncedSyncWorkflowDraft, doSyncWorkflowDraft, getNodesReadOnly])

  return {
    doSyncWorkflowDraft,
    handleSyncWorkflowDraft,
    syncWorkflowDraftWhenPageClose,
  }
}
